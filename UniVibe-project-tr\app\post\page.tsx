"use client"

import type React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Calendar, Clock, MapPin, Upload, Save, Sparkles, ImageIcon, X, FileText, Users, Tag } from "lucide-react"
import { useState, useRef } from "react"
import { createEvent, saveDraft, validateEventForm, uploadEventImage, type EventFormData } from "@/lib/events"
import { showSuccess, showError, showEventCreatedNotification, showWarning } from "@/components/ui/notification"

const categories = [
  { value: "Music", label: "🎵 Music", description: "Concerts, festivals, open mics" },
  { value: "Academic", label: "📚 Academic", description: "Lectures, workshops, seminars" },
  { value: "Sports", label: "⚽ Sports", description: "Games, tournaments, fitness" },
  { value: "Food", label: "🍕 Food", description: "Food trucks, tastings, cooking" },
  { value: "Arts", label: "🎨 Arts", description: "Exhibitions, performances, crafts" },
  { value: "Clubs", label: "👥 Clubs", description: "Club meetings, socials" },
  { value: "Other", label: "✨ Other", description: "Everything else amazing" }
]

export default function PostEventPage() {
  const [formData, setFormData] = useState<EventFormData>({
    title: "",
    description: "",
    date: "",
    time: "",
    venue: "",
    category: "",
    image: null,
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSavingDraft, setIsSavingDraft] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [errors, setErrors] = useState<string[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setErrors([])

    // Validate form
    const validation = validateEventForm(formData)
    if (!validation.isValid) {
      setErrors(validation.errors)
      setIsSubmitting(false)
      showError("Please fix the errors below", validation.errors.join(", "))
      return
    }

    try {
      let imageUrl = null

      // Upload image if provided
      if (formData.image) {
        setUploadProgress(25)
        const tempEventId = `event_${Date.now()}`
        console.log('Starting image upload for event:', tempEventId)

        const { url, error: uploadError } = await uploadEventImage(formData.image, tempEventId)

        if (uploadError) {
          console.error('Image upload failed:', uploadError)
          showError("Image upload failed", uploadError)
          setIsSubmitting(false)
          setUploadProgress(0)
          return
        }

        console.log('Image uploaded successfully:', url)
        imageUrl = url
        setUploadProgress(50)
      }

      setUploadProgress(75)

      // Create event
      const { data, error } = await createEvent({
        title: formData.title,
        description: formData.description,
        date: formData.date,
        time: formData.time,
        location: formData.venue,
        category: formData.category,
        organizer: "Current User", // TODO: Get from auth context
        image_url: imageUrl,
      })

      setUploadProgress(100)

      if (error) {
        showError("Failed to create event", error)
        setIsSubmitting(false)
        return
      }

      // Success!
      showEventCreatedNotification(formData.title, false, data?.id)
      resetForm()
    } catch (error) {
      console.error("Error creating event:", error)
      showError("Something went wrong", "Please try again later")
    } finally {
      setIsSubmitting(false)
      setUploadProgress(0)
    }
  }

  const handleSaveDraft = async () => {
    setIsSavingDraft(true)
    setErrors([])

    try {
      const { data, error } = await saveDraft({
        title: formData.title || "Untitled Event",
        description: formData.description,
        date: formData.date,
        time: formData.time,
        location: formData.venue,
        category: formData.category,
        organizer: "Current User", // TODO: Get from auth context
      })

      if (error) {
        showError("Failed to save draft", error)
        return
      }

      showEventCreatedNotification(formData.title || "Untitled Event", true)
    } catch (error) {
      console.error("Error saving draft:", error)
      showError("Failed to save draft", "Please try again later")
    } finally {
      setIsSavingDraft(false)
    }
  }

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      date: "",
      time: "",
      venue: "",
      category: "",
      image: null,
    })
    setImagePreview(null)
    setErrors([])
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        showError("Invalid file type", "Please select an image file")
        return
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        showError("File too large", "Please select an image smaller than 5MB")
        return
      }

      setFormData((prev) => ({ ...prev, image: file }))

      // Create preview
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const removeImage = () => {
    setFormData((prev) => ({ ...prev, image: null }))
    setImagePreview(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const getFormProgress = () => {
    const fields = [
      formData.title,
      formData.description,
      formData.date,
      formData.time,
      formData.venue,
      formData.category
    ]
    const filledFields = fields.filter(field => field.trim() !== "").length
    return Math.round((filledFields / fields.length) * 100)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-accent/5 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center gap-3 mb-4">
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-xl flex items-center justify-center shadow-lg">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <div className="absolute -inset-1 bg-gradient-to-br from-primary to-accent rounded-xl blur opacity-30 animate-pulse"></div>
            </div>
            <div>
              <h1 className="text-3xl font-bold gradient-text">Create Amazing Event</h1>
              <p className="text-muted-foreground">Share your event with the campus community</p>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="max-w-md mx-auto">
            <div className="flex items-center gap-2 mb-2">
              <FileText className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Form Progress</span>
              <Badge variant="secondary" className="ml-auto">
                {getFormProgress()}%
              </Badge>
            </div>
            <Progress value={getFormProgress()} className="h-2" />
          </div>
        </div>

        {/* Error Display */}
        {errors.length > 0 && (
          <Card className="mb-6 border-red-500/20 bg-red-500/5">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <X className="h-5 w-5 text-red-500 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-red-700 dark:text-red-300 mb-2">Please fix these issues:</h3>
                  <ul className="space-y-1">
                    {errors.map((error, index) => (
                      <li key={index} className="text-sm text-red-600 dark:text-red-400">• {error}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Card className="shadow-xl border-white/10">
          <CardContent className="p-8">
            <form onSubmit={handleSubmit} className="space-y-8">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Left Column - Main Details */}
                <div className="lg:col-span-2 space-y-6">
                  <div className="space-y-6">
                    <div>
                      <Label htmlFor="title" className="text-base font-semibold flex items-center gap-2">
                        <Sparkles className="h-4 w-4" />
                        Event Title
                      </Label>
                      <Input
                        id="title"
                        value={formData.title}
                        onChange={(e) => setFormData((prev) => ({ ...prev, title: e.target.value }))}
                        placeholder="🎉 Spring Music Festival 2024"
                        className="mt-2 text-lg h-12"
                        required
                      />
                      <p className="text-xs text-muted-foreground mt-1">Make it catchy and memorable!</p>
                    </div>

                    <div>
                      <Label htmlFor="description" className="text-base font-semibold flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        Event Description
                      </Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                        placeholder="🎵 Join us for an unforgettable night of music, food, and fun!

✨ What to expect:
• Live performances from local bands
• Food trucks and refreshments
• Prizes and giveaways
• Meet new friends and dance the night away!

📍 Don't forget to bring your student ID for entry!"
                        rows={8}
                        className="mt-2 resize-none"
                        required
                      />
                      <p className="text-xs text-muted-foreground mt-1">Paint a picture! Use emojis and bullet points to make it engaging.</p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="date" className="text-base font-semibold flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          Event Date
                        </Label>
                        <div className="relative mt-2">
                          <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                          <Input
                            id="date"
                            type="date"
                            value={formData.date}
                            onChange={(e) => setFormData((prev) => ({ ...prev, date: e.target.value }))}
                            className="pl-10 h-12"
                            min={new Date().toISOString().split('T')[0]}
                            required
                          />
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="time" className="text-base font-semibold flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          Start Time
                        </Label>
                        <div className="relative mt-2">
                          <Clock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                          <Input
                            id="time"
                            type="time"
                            value={formData.time}
                            onChange={(e) => setFormData((prev) => ({ ...prev, time: e.target.value }))}
                            className="pl-10 h-12"
                            required
                          />
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="venue" className="text-base font-semibold flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        Venue Location
                      </Label>
                      <div className="relative mt-2">
                        <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="venue"
                          value={formData.venue}
                          onChange={(e) => setFormData((prev) => ({ ...prev, venue: e.target.value }))}
                          placeholder="🏛️ University Quad, Student Center, Room 101..."
                          className="pl-10 h-12"
                          required
                        />
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">Be specific! Help people find you easily.</p>
                    </div>

                    <div>
                      <Label htmlFor="category" className="text-base font-semibold flex items-center gap-2">
                        <Tag className="h-4 w-4" />
                        Event Category
                      </Label>
                      <Select value={formData.category} onValueChange={(value) => setFormData((prev) => ({ ...prev, category: value }))}>
                        <SelectTrigger className="mt-2 h-12">
                          <SelectValue placeholder="🎯 Choose the perfect category..." />
                        </SelectTrigger>
                        <SelectContent>
                          {categories.map((category) => (
                            <SelectItem key={category.value} value={category.value}>
                              <div className="flex items-center gap-2">
                                <span>{category.label}</span>
                                <span className="text-xs text-muted-foreground">- {category.description}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                {/* Right Column - Event Poster */}
                <div className="space-y-6">
                  <div>
                    <Label className="text-base font-semibold flex items-center gap-2 mb-4">
                      <ImageIcon className="h-4 w-4" />
                      Event Poster
                    </Label>

                    {!imagePreview ? (
                      <div
                        className="border-2 border-dashed border-primary/25 rounded-xl p-8 text-center hover:border-primary/50 transition-all duration-300 cursor-pointer bg-gradient-to-br from-primary/5 to-accent/5 hover:from-primary/10 hover:to-accent/10"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        <div className="relative mx-auto w-16 h-16 mb-4">
                          <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-accent/20 rounded-xl flex items-center justify-center">
                            <Upload className="h-8 w-8 text-primary" />
                          </div>
                          <Sparkles className="absolute -top-1 -right-1 h-4 w-4 text-yellow-400 animate-pulse" />
                        </div>
                        <h3 className="font-semibold text-lg mb-2">Add Some Visual Magic! ✨</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          Upload a stunning poster to grab attention
                        </p>
                        <div className="space-y-2 text-xs text-muted-foreground">
                          <p>📸 JPG, PNG, or GIF</p>
                          <p>📏 Max 5MB</p>
                          <p>🎨 Square images work best</p>
                        </div>
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={handleImageUpload}
                          className="hidden"
                        />
                      </div>
                    ) : (
                      <div className="relative rounded-xl overflow-hidden border-2 border-primary/20">
                        <img
                          src={imagePreview}
                          alt="Event preview"
                          className="w-full h-64 object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                        <button
                          type="button"
                          onClick={removeImage}
                          className="absolute top-3 right-3 p-2 bg-red-500 hover:bg-red-600 text-white rounded-full transition-colors"
                        >
                          <X className="h-4 w-4" />
                        </button>
                        <div className="absolute bottom-3 left-3 right-3">
                          <p className="text-white text-sm font-medium">Looking great! 🎉</p>
                          <p className="text-white/80 text-xs">Click the X to change image</p>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Quick Stats */}
                  <Card className="bg-gradient-to-br from-primary/5 to-accent/5 border-primary/20">
                    <CardContent className="p-4">
                      <h3 className="font-semibold mb-3 flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        Event Impact
                      </h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Expected reach:</span>
                          <span className="font-medium">500+ students</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Visibility:</span>
                          <span className="font-medium">Campus-wide</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Duration:</span>
                          <span className="font-medium">Permanent listing</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>

              {/* Upload Progress */}
              {uploadProgress > 0 && (
                <Card className="border-primary/20 bg-primary/5">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3 mb-2">
                      <Sparkles className="h-4 w-4 text-primary animate-spin" />
                      <span className="font-medium">Creating your amazing event...</span>
                    </div>
                    <Progress value={uploadProgress} className="h-2" />
                  </CardContent>
                </Card>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-white/10">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex-1 h-12 text-lg font-semibold bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg"
                >
                  {isSubmitting ? (
                    <>
                      <Sparkles className="h-5 w-5 mr-2 animate-spin" />
                      Creating Magic...
                    </>
                  ) : (
                    <>
                      🚀 Publish Event
                    </>
                  )}
                </Button>

                <Button
                  type="button"
                  variant="outline"
                  onClick={handleSaveDraft}
                  disabled={isSavingDraft}
                  className="flex-1 sm:flex-none h-12 border-primary/20 hover:bg-primary/5"
                >
                  {isSavingDraft ? (
                    <>
                      <Save className="h-4 w-4 mr-2 animate-pulse" />
                      Saving...
                    </>
                  ) : (
                    <>
                      💾 Save Draft
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
