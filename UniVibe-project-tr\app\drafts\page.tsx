"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, MapPin, Trash2, Send, FileText, Sparkles } from "lucide-react"
import { useState, useEffect } from "react"
import { getDrafts, deleteDraft, publishDraft } from "@/lib/events"
import { showEventCreatedNotification, showError } from "@/components/ui/notification"

interface Draft {
  id: string
  title: string
  description: string
  date: string
  time: string
  location: string
  category: string
  organizer: string
  created_at: string
  updated_at: string
}

export default function DraftsPage() {
  const [drafts, setDrafts] = useState<Draft[]>([])
  const [publishingDrafts, setPublishingDrafts] = useState<Set<string>>(new Set())
  const [deletingDrafts, setDeletingDrafts] = useState<Set<string>>(new Set())

  // Load drafts on component mount
  useEffect(() => {
    loadDrafts()
  }, [])

  const loadDrafts = () => {
    const draftData = getDrafts()
    const draftArray = Object.values(draftData) as Draft[]
    // Sort by most recently updated
    draftArray.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
    setDrafts(draftArray)
  }

  const handlePublishDraft = async (draftId: string, draftTitle: string) => {
    setPublishingDrafts(prev => new Set(prev).add(draftId))

    try {
      const { data, error } = await publishDraft(draftId)

      if (error) {
        showError("Failed to publish draft", error)
        return
      }

      // Success!
      showEventCreatedNotification(draftTitle, false)
      loadDrafts() // Refresh the drafts list
    } catch (error) {
      console.error("Error publishing draft:", error)
      showError("Something went wrong", "Please try again later")
    } finally {
      setPublishingDrafts(prev => {
        const newSet = new Set(prev)
        newSet.delete(draftId)
        return newSet
      })
    }
  }

  const handleDeleteDraft = async (draftId: string) => {
    setDeletingDrafts(prev => new Set(prev).add(draftId))

    try {
      const success = deleteDraft(draftId)
      if (success) {
        loadDrafts() // Refresh the drafts list
      } else {
        showError("Failed to delete draft", "Please try again")
      }
    } catch (error) {
      console.error("Error deleting draft:", error)
      showError("Something went wrong", "Please try again later")
    } finally {
      setDeletingDrafts(prev => {
        const newSet = new Set(prev)
        newSet.delete(draftId)
        return newSet
      })
    }
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return "No date set"
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    })
  }

  const formatTime = (timeString: string) => {
    if (!timeString) return "No time set"
    const [hours, minutes] = timeString.split(':')
    const date = new Date()
    date.setHours(parseInt(hours), parseInt(minutes))
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 rounded-xl bg-accent/10 border border-accent/20">
              <FileText className="h-6 w-6 text-accent" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                📝 My Drafts
              </h1>
              <p className="text-muted-foreground">
                Manage your saved event drafts and publish them when ready
              </p>
            </div>
          </div>
        </div>

        {/* Drafts List */}
        {drafts.length === 0 ? (
          <Card className="shadow-xl border-white/10">
            <CardContent className="p-12 text-center">
              <div className="p-4 rounded-full bg-muted/20 w-fit mx-auto mb-4">
                <FileText className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-2">No drafts yet</h3>
              <p className="text-muted-foreground mb-6">
                Start creating events and save them as drafts to see them here
              </p>
              <Button asChild className="bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90">
                <a href="/post">
                  <Sparkles className="h-4 w-4 mr-2" />
                  Create Event
                </a>
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-6">
            {drafts.map((draft) => (
              <Card key={draft.id} className="shadow-xl border-white/10 hover:shadow-2xl transition-all duration-300">
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-xl mb-2 flex items-center gap-2">
                        {draft.title || "Untitled Event"}
                        <Badge variant="outline" className="text-xs">
                          Draft
                        </Badge>
                      </CardTitle>
                      {draft.category && (
                        <Badge variant="secondary" className="mb-2">
                          {draft.category}
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Description */}
                  {draft.description && (
                    <p className="text-muted-foreground line-clamp-3">
                      {draft.description}
                    </p>
                  )}

                  {/* Event Details */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>{formatDate(draft.date)}</span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      <span>{formatTime(draft.time)}</span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <MapPin className="h-4 w-4" />
                      <span>{draft.location || "No location set"}</span>
                    </div>
                  </div>

                  {/* Draft Metadata */}
                  <div className="text-xs text-muted-foreground border-t border-white/10 pt-4">
                    <p>Created: {new Date(draft.created_at).toLocaleDateString()}</p>
                    <p>Last updated: {new Date(draft.updated_at).toLocaleDateString()}</p>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-3 pt-4 border-t border-white/10">
                    <Button
                      onClick={() => handlePublishDraft(draft.id, draft.title)}
                      disabled={publishingDrafts.has(draft.id)}
                      className="flex-1 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90"
                    >
                      {publishingDrafts.has(draft.id) ? (
                        <>
                          <Sparkles className="h-4 w-4 mr-2 animate-spin" />
                          Publishing...
                        </>
                      ) : (
                        <>
                          <Send className="h-4 w-4 mr-2" />
                          🚀 Publish Event
                        </>
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => handleDeleteDraft(draft.id)}
                      disabled={deletingDrafts.has(draft.id)}
                      className="border-destructive/20 hover:bg-destructive/10 hover:text-destructive"
                    >
                      {deletingDrafts.has(draft.id) ? (
                        <Sparkles className="h-4 w-4 animate-spin" />
                      ) : (
                        <Trash2 className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
