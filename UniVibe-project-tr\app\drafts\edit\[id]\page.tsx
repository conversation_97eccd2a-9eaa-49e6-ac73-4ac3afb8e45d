"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Save, Trash2, Send, Sparkles, Calendar, Clock, MapPin, User, FileText } from "lucide-react"
import { useState, useEffect, use } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { getDrafts, saveDraft, deleteDraft, publishDraft } from "@/lib/events"
import { showEventCreatedNotification, showError, showSuccess } from "@/components/ui/notification"

interface Draft {
  id: string
  title: string
  description: string
  date: string
  time: string
  location: string
  category: string
  organizer: string
  organizer_id?: string
  image_url?: string
  created_at: string
  updated_at: string
}

export default function EditDraftPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params)
  const router = useRouter()
  const [draft, setDraft] = useState<Draft | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [publishing, setPublishing] = useState(false)
  const [deleting, setDeleting] = useState(false)

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    date: "",
    time: "",
    location: "",
    category: "",
    organizer: "",
  })

  const categories = ["Academic", "Music", "Sports", "Food", "Arts", "Clubs", "Social", "Career", "Health", "Technology"]

  // Load draft data
  useEffect(() => {
    const loadDraft = () => {
      try {
        const drafts = getDrafts()
        const foundDraft = drafts[resolvedParams.id]
        
        if (foundDraft) {
          setDraft(foundDraft)
          setFormData({
            title: foundDraft.title || "",
            description: foundDraft.description || "",
            date: foundDraft.date || "",
            time: foundDraft.time || "",
            location: foundDraft.location || "",
            category: foundDraft.category || "",
            organizer: foundDraft.organizer || "",
          })
        } else {
          showError("Draft not found", "The draft you're looking for doesn't exist.")
          router.push("/drafts")
        }
      } catch (error) {
        console.error("Error loading draft:", error)
        showError("Error loading draft", "Please try again.")
        router.push("/drafts")
      } finally {
        setLoading(false)
      }
    }

    loadDraft()
  }, [resolvedParams.id, router])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      const success = saveDraft({
        ...formData,
        id: resolvedParams.id,
        organizer_id: draft?.organizer_id,
        image_url: draft?.image_url,
        created_at: draft?.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })

      if (success) {
        showSuccess("Draft saved", "Your changes have been saved successfully.")
        // Refresh draft data
        const drafts = getDrafts()
        const updatedDraft = drafts[resolvedParams.id]
        if (updatedDraft) {
          setDraft(updatedDraft)
        }
      } else {
        showError("Failed to save", "Please try again.")
      }
    } catch (error) {
      console.error("Error saving draft:", error)
      showError("Something went wrong", "Please try again later.")
    } finally {
      setSaving(false)
    }
  }

  const handlePublish = async () => {
    setPublishing(true)
    try {
      // Save current changes first
      const saveSuccess = saveDraft({
        ...formData,
        id: resolvedParams.id,
        organizer_id: draft?.organizer_id,
        image_url: draft?.image_url,
        created_at: draft?.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })

      if (!saveSuccess) {
        showError("Failed to save changes", "Please try again.")
        return
      }

      // Publish the draft
      const { data, error } = await publishDraft(resolvedParams.id)

      if (error) {
        showError("Failed to publish draft", error)
        return
      }

      // Success!
      showEventCreatedNotification(formData.title, false, data?.id)
      router.push("/drafts")
    } catch (error) {
      console.error("Error publishing draft:", error)
      showError("Something went wrong", "Please try again later.")
    } finally {
      setPublishing(false)
    }
  }

  const handleDelete = async () => {
    if (!confirm("Are you sure you want to delete this draft? This action cannot be undone.")) {
      return
    }

    setDeleting(true)
    try {
      const success = deleteDraft(resolvedParams.id)
      if (success) {
        showSuccess("Draft deleted", "The draft has been deleted successfully.")
        router.push("/drafts")
      } else {
        showError("Failed to delete draft", "Please try again.")
      }
    } catch (error) {
      console.error("Error deleting draft:", error)
      showError("Something went wrong", "Please try again later.")
    } finally {
      setDeleting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 p-4">
        <div className="container mx-auto max-w-4xl">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-muted rounded w-1/3"></div>
            <div className="h-64 bg-muted rounded"></div>
            <div className="h-32 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!draft) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 p-4">
        <div className="container mx-auto max-w-4xl text-center py-16">
          <h1 className="text-2xl font-bold mb-4">Draft Not Found</h1>
          <p className="text-muted-foreground mb-6">The draft you're looking for doesn't exist.</p>
          <Link href="/drafts">
            <Button>Back to Drafts</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link href="/drafts">
            <Button variant="ghost" size="icon" className="hover:bg-accent/10">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div className="flex items-center gap-3">
            <div className="p-3 rounded-xl bg-gradient-to-br from-accent/20 to-primary/20 border border-accent/30">
              <FileText className="h-6 w-6 text-accent" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                Edit Draft
              </h1>
              <p className="text-muted-foreground">
                Refine your event details and publish when ready
              </p>
            </div>
          </div>
        </div>

        {/* Edit Form */}
        <Card className="shadow-xl border-white/10 bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-accent" />
              Event Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Title */}
            <div className="space-y-2">
              <Label htmlFor="title" className="text-sm font-medium">Event Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
                placeholder="Enter an exciting event title..."
                className="bg-background/50 border-white/20 focus:border-accent/50"
              />
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                placeholder="Describe your event in detail..."
                rows={4}
                className="bg-background/50 border-white/20 focus:border-accent/50 resize-none"
              />
            </div>

            {/* Date and Time */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="date" className="text-sm font-medium flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Event Date *
                </Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange("date", e.target.value)}
                  className="bg-background/50 border-white/20 focus:border-accent/50"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="time" className="text-sm font-medium flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Event Time *
                </Label>
                <Input
                  id="time"
                  type="time"
                  value={formData.time}
                  onChange={(e) => handleInputChange("time", e.target.value)}
                  className="bg-background/50 border-white/20 focus:border-accent/50"
                />
              </div>
            </div>

            {/* Location and Category */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="location" className="text-sm font-medium flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Location *
                </Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => handleInputChange("location", e.target.value)}
                  placeholder="Where will this event take place?"
                  className="bg-background/50 border-white/20 focus:border-accent/50"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category" className="text-sm font-medium">Category *</Label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange("category", value)}>
                  <SelectTrigger className="bg-background/50 border-white/20 focus:border-accent/50">
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Organizer */}
            <div className="space-y-2">
              <Label htmlFor="organizer" className="text-sm font-medium flex items-center gap-2">
                <User className="h-4 w-4" />
                Organizer *
              </Label>
              <Input
                id="organizer"
                value={formData.organizer}
                onChange={(e) => handleInputChange("organizer", e.target.value)}
                placeholder="Who is organizing this event?"
                className="bg-background/50 border-white/20 focus:border-accent/50"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t border-white/10">
              <Button
                onClick={handleSave}
                disabled={saving}
                variant="outline"
                className="flex-1 border-white/20 hover:bg-accent/10"
              >
                {saving ? (
                  <>
                    <Sparkles className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </>
                )}
              </Button>
              
              <Button
                onClick={handlePublish}
                disabled={publishing || !formData.title || !formData.date || !formData.time || !formData.location || !formData.category || !formData.organizer}
                className="flex-1 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90"
              >
                {publishing ? (
                  <>
                    <Sparkles className="h-4 w-4 mr-2 animate-spin" />
                    Publishing...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    🚀 Publish Event
                  </>
                )}
              </Button>

              <Button
                onClick={handleDelete}
                disabled={deleting}
                variant="outline"
                className="border-destructive/20 hover:bg-destructive/10 hover:text-destructive"
              >
                {deleting ? (
                  <Sparkles className="h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4" />
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
