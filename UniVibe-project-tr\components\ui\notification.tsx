"use client"

import React from 'react'
import { toast } from 'sonner'
import { CheckCircle, XCircle, AlertTriangle, Info, Sparkles, Heart, Calendar, Users } from 'lucide-react'
import { cn } from '@/lib/utils'

interface NotificationProps {
  title: string
  description?: string
  type?: 'success' | 'error' | 'warning' | 'info' | 'event' | 'rsvp'
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

const notificationConfig = {
  success: {
    icon: CheckCircle,
    className: 'bg-gradient-to-r from-green-500/10 to-emerald-500/10 border-green-500/20 text-green-700 dark:text-green-300',
    iconClassName: 'text-green-500',
  },
  error: {
    icon: XCircle,
    className: 'bg-gradient-to-r from-red-500/10 to-rose-500/10 border-red-500/20 text-red-700 dark:text-red-300',
    iconClassName: 'text-red-500',
  },
  warning: {
    icon: AlertTriangle,
    className: 'bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border-yellow-500/20 text-yellow-700 dark:text-yellow-300',
    iconClassName: 'text-yellow-500',
  },
  info: {
    icon: Info,
    className: 'bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border-blue-500/20 text-blue-700 dark:text-blue-300',
    iconClassName: 'text-blue-500',
  },
  event: {
    icon: Calendar,
    className: 'bg-gradient-to-r from-purple-500/10 to-pink-500/10 border-purple-500/20 text-purple-700 dark:text-purple-300',
    iconClassName: 'text-purple-500',
  },
  rsvp: {
    icon: Heart,
    className: 'bg-gradient-to-r from-pink-500/10 to-rose-500/10 border-pink-500/20 text-pink-700 dark:text-pink-300',
    iconClassName: 'text-pink-500',
  },
}

const CustomNotification = ({ title, description, type = 'info', action }: Omit<NotificationProps, 'duration'>) => {
  const config = notificationConfig[type]
  const Icon = config.icon

  return (
    <div className={cn(
      'flex items-start gap-3 p-4 rounded-xl border backdrop-blur-sm shadow-lg',
      'animate-in slide-in-from-right-full duration-300',
      config.className
    )}>
      <div className="flex-shrink-0">
        <div className="relative">
          <Icon className={cn('h-5 w-5', config.iconClassName)} />
          {type === 'event' && (
            <Sparkles className="absolute -top-1 -right-1 h-3 w-3 text-yellow-400 animate-pulse" />
          )}
        </div>
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="font-semibold text-sm leading-tight">{title}</div>
        {description && (
          <div className="text-xs opacity-80 mt-1 leading-relaxed">{description}</div>
        )}
        
        {action && (
          <button
            onClick={action.onClick}
            className={cn(
              'mt-2 text-xs font-medium px-3 py-1 rounded-full',
              'bg-white/20 hover:bg-white/30 transition-colors',
              'border border-white/20'
            )}
          >
            {action.label}
          </button>
        )}
      </div>
    </div>
  )
}

// Enhanced notification functions
export const showNotification = ({
  title,
  description,
  type = 'info',
  duration = 4000,
  action
}: NotificationProps) => {
  toast.custom(
    () => (
      <CustomNotification
        title={title}
        description={description}
        type={type}
        action={action}
      />
    ),
    {
      duration,
      position: 'top-right',
    }
  )
}

// Convenience functions for different notification types
export const showSuccess = (title: string, description?: string, action?: NotificationProps['action']) => {
  showNotification({ title, description, type: 'success', action })
}

export const showError = (title: string, description?: string, action?: NotificationProps['action']) => {
  showNotification({ title, description, type: 'error', action })
}

export const showWarning = (title: string, description?: string, action?: NotificationProps['action']) => {
  showNotification({ title, description, type: 'warning', action })
}

export const showInfo = (title: string, description?: string, action?: NotificationProps['action']) => {
  showNotification({ title, description, type: 'info', action })
}

export const showEventNotification = (title: string, description?: string, action?: NotificationProps['action']) => {
  showNotification({ title, description, type: 'event', duration: 6000, action })
}

export const showRSVPNotification = (title: string, description?: string, action?: NotificationProps['action']) => {
  showNotification({ title, description, type: 'rsvp', duration: 5000, action })
}

// Special celebration notification for successful event creation
export const showEventCreatedNotification = (eventTitle: string, isDraft = false) => {
  const title = isDraft ? '📝 Draft Saved!' : '🎉 Event Created!'
  const description = isDraft 
    ? `"${eventTitle}" has been saved as a draft. You can publish it later.`
    : `"${eventTitle}" is now live! Students can start discovering and RSVPing to your event.`
  
  showNotification({
    title,
    description,
    type: isDraft ? 'info' : 'event',
    duration: isDraft ? 4000 : 6000,
    action: isDraft ? undefined : {
      label: 'View Event',
      onClick: () => {
        // Navigate to event page
        console.log('Navigate to event')
      }
    }
  })
}
