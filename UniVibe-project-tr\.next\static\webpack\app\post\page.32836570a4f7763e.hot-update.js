"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/post/page",{

/***/ "(app-pages-browser)/./lib/events.ts":
/*!***********************!*\
  !*** ./lib/events.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEvent: () => (/* binding */ createEvent),\n/* harmony export */   deleteDraft: () => (/* binding */ deleteDraft),\n/* harmony export */   deleteEvent: () => (/* binding */ deleteEvent),\n/* harmony export */   getDrafts: () => (/* binding */ getDrafts),\n/* harmony export */   getEventById: () => (/* binding */ getEventById),\n/* harmony export */   getEvents: () => (/* binding */ getEvents),\n/* harmony export */   publishDraft: () => (/* binding */ publishDraft),\n/* harmony export */   saveDraft: () => (/* binding */ saveDraft),\n/* harmony export */   updateEvent: () => (/* binding */ updateEvent),\n/* harmony export */   uploadEventImage: () => (/* binding */ uploadEventImage),\n/* harmony export */   validateEventForm: () => (/* binding */ validateEventForm)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n\n// Create a new event\nasync function createEvent(eventData) {\n    try {\n        console.log('Creating event with data:', eventData);\n        const insertData = {\n            title: eventData.title,\n            description: eventData.description,\n            date: eventData.date,\n            time: eventData.time,\n            location: eventData.location,\n            category: eventData.category,\n            organizer: eventData.organizer,\n            organizer_id: eventData.organizer_id,\n            image_url: eventData.image_url,\n            attendees_count: 0\n        };\n        console.log('Insert data:', insertData);\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').insert(insertData).select().single();\n        if (error) {\n            console.error('Error creating event:', error);\n            return {\n                data: null,\n                error: error.message || 'Unknown database error'\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error creating event:', error);\n        return {\n            data: null,\n            error: 'An unexpected error occurred'\n        };\n    }\n}\n// Save event as draft (using a separate drafts table or a flag)\nasync function saveDraft(eventData) {\n    try {\n        // For now, we'll save drafts in localStorage\n        // In a real app, you might want a separate drafts table or a is_draft flag\n        const drafts = getDrafts();\n        // Use existing ID if provided (for updates), otherwise create new one\n        const draftId = eventData.id || \"draft_\".concat(Date.now());\n        const existingDraft = drafts[draftId];\n        const draftToSave = {\n            id: draftId,\n            ...eventData,\n            created_at: eventData.created_at || (existingDraft === null || existingDraft === void 0 ? void 0 : existingDraft.created_at) || new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        drafts[draftId] = draftToSave;\n        localStorage.setItem('event_drafts', JSON.stringify(drafts));\n        return {\n            data: draftToSave,\n            error: null\n        };\n    } catch (error) {\n        console.error('Error saving draft:', error);\n        return {\n            data: null,\n            error: 'Failed to save draft'\n        };\n    }\n}\n// Get all drafts\nfunction getDrafts() {\n    try {\n        const drafts = localStorage.getItem('event_drafts');\n        return drafts ? JSON.parse(drafts) : {};\n    } catch (error) {\n        console.error('Error getting drafts:', error);\n        return {};\n    }\n}\n// Delete a draft\nfunction deleteDraft(draftId) {\n    try {\n        const drafts = getDrafts();\n        delete drafts[draftId];\n        localStorage.setItem('event_drafts', JSON.stringify(drafts));\n        return true;\n    } catch (error) {\n        console.error('Error deleting draft:', error);\n        return false;\n    }\n}\n// Publish a draft as a live event\nasync function publishDraft(draftId) {\n    try {\n        const drafts = getDrafts();\n        const draft = drafts[draftId];\n        if (!draft) {\n            return {\n                data: null,\n                error: 'Draft not found'\n            };\n        }\n        // Validate required fields\n        if (!draft.title || !draft.date || !draft.time || !draft.location || !draft.category || !draft.organizer) {\n            return {\n                data: null,\n                error: 'Draft is missing required fields. Please edit the draft and ensure all fields are filled.'\n            };\n        }\n        // Create the event from the draft\n        const { data, error } = await createEvent({\n            title: draft.title,\n            description: draft.description || '',\n            date: draft.date,\n            time: draft.time,\n            location: draft.location,\n            category: draft.category,\n            organizer: draft.organizer,\n            organizer_id: draft.organizer_id || null,\n            image_url: draft.image_url || null\n        });\n        if (error) {\n            return {\n                data: null,\n                error\n            };\n        }\n        // Delete the draft after successful publication\n        deleteDraft(draftId);\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Error publishing draft:', error);\n        return {\n            data: null,\n            error: 'Failed to publish draft'\n        };\n    }\n}\n// Upload image to Supabase Storage\nasync function uploadEventImage(file, eventId) {\n    try {\n        console.log('Uploading image:', {\n            fileName: file.name,\n            fileSize: file.size,\n            eventId\n        });\n        // Validate file\n        if (!file) {\n            return {\n                url: null,\n                error: 'No file provided'\n            };\n        }\n        // Check file size (50MB limit)\n        if (file.size > 50 * 1024 * 1024) {\n            return {\n                url: null,\n                error: 'File size too large. Maximum size is 50MB.'\n            };\n        }\n        // Check file type\n        const allowedTypes = [\n            'image/jpeg',\n            'image/png',\n            'image/webp',\n            'image/gif'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            return {\n                url: null,\n                error: 'Invalid file type. Please upload a JPEG, PNG, WebP, or GIF image.'\n            };\n        }\n        const fileExt = file.name.split('.').pop();\n        const fileName = \"\".concat(eventId, \"_\").concat(Date.now(), \".\").concat(fileExt);\n        const filePath = \"event-images/\".concat(fileName);\n        console.log('Uploading to path:', filePath);\n        const { data: uploadData, error: uploadError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from('events').upload(filePath, file, {\n            cacheControl: '3600',\n            upsert: true\n        });\n        if (uploadError) {\n            console.error('Error uploading image:', uploadError);\n            return {\n                url: null,\n                error: uploadError.message || 'Failed to upload image to storage'\n            };\n        }\n        console.log('Upload successful:', uploadData);\n        const { data } = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from('events').getPublicUrl(filePath);\n        console.log('Public URL generated:', data.publicUrl);\n        return {\n            url: data.publicUrl,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error uploading image:', error);\n        return {\n            url: null,\n            error: 'Failed to upload image'\n        };\n    }\n}\n// Get all events\nasync function getEvents() {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').select('*').order('date', {\n            ascending: true\n        });\n        if (error) {\n            console.error('Error fetching events:', error);\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error fetching events:', error);\n        return {\n            data: null,\n            error: 'Failed to fetch events'\n        };\n    }\n}\n// Get event by ID\nasync function getEventById(id) {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').select('*').eq('id', id).single();\n        if (error) {\n            console.error('Error fetching event:', error);\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error fetching event:', error);\n        return {\n            data: null,\n            error: 'Failed to fetch event'\n        };\n    }\n}\n// Update event\nasync function updateEvent(id, updates) {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').update(updates).eq('id', id).select().single();\n        if (error) {\n            console.error('Error updating event:', error);\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error updating event:', error);\n        return {\n            data: null,\n            error: 'Failed to update event'\n        };\n    }\n}\n// Delete event\nasync function deleteEvent(id) {\n    try {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').delete().eq('id', id);\n        if (error) {\n            console.error('Error deleting event:', error);\n            return {\n                error: error.message\n            };\n        }\n        return {\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error deleting event:', error);\n        return {\n            error: 'Failed to delete event'\n        };\n    }\n}\n// Validate event form data\nfunction validateEventForm(formData) {\n    const errors = [];\n    if (!formData.title.trim()) {\n        errors.push('Event title is required');\n    }\n    if (!formData.description.trim()) {\n        errors.push('Event description is required');\n    }\n    if (!formData.date) {\n        errors.push('Event date is required');\n    } else {\n        const eventDate = new Date(formData.date);\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        if (eventDate < today) {\n            errors.push('Event date cannot be in the past');\n        }\n    }\n    if (!formData.time) {\n        errors.push('Event time is required');\n    }\n    if (!formData.venue.trim()) {\n        errors.push('Event venue is required');\n    }\n    if (!formData.category) {\n        errors.push('Event category is required');\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/events.ts\n"));

/***/ })

});