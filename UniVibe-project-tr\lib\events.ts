import { supabase } from './supabase'
import type { Database } from './supabase'

type Event = Database['public']['Tables']['events']['Row']
type EventInsert = Database['public']['Tables']['events']['Insert']
type EventUpdate = Database['public']['Tables']['events']['Update']

export interface CreateEventData {
  title: string
  description: string
  date: string
  time: string
  location: string
  category: string
  organizer: string
  organizer_id?: string
  image_url?: string
  is_draft?: boolean
}

export interface EventFormData {
  title: string
  description: string
  date: string
  time: string
  venue: string
  category: string
  image: File | null
}

// Create a new event
export async function createEvent(eventData: CreateEventData): Promise<{ data: Event | null; error: string | null }> {
  try {
    const { data, error } = await supabase
      .from('events')
      .insert({
        title: eventData.title,
        description: eventData.description,
        date: eventData.date,
        time: eventData.time,
        location: eventData.location,
        category: eventData.category,
        organizer: eventData.organizer,
        organizer_id: eventData.organizer_id,
        image_url: eventData.image_url,
        attendees_count: 0
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating event:', error)
      return { data: null, error: error.message }
    }

    return { data, error: null }
  } catch (error) {
    console.error('Unexpected error creating event:', error)
    return { data: null, error: 'An unexpected error occurred' }
  }
}

// Save event as draft (using a separate drafts table or a flag)
export async function saveDraft(eventData: CreateEventData): Promise<{ data: any | null; error: string | null }> {
  try {
    // For now, we'll save drafts in localStorage
    // In a real app, you might want a separate drafts table or a is_draft flag
    const drafts = getDrafts()
    const draftId = `draft_${Date.now()}`
    const newDraft = {
      id: draftId,
      ...eventData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    
    drafts[draftId] = newDraft
    localStorage.setItem('event_drafts', JSON.stringify(drafts))
    
    return { data: newDraft, error: null }
  } catch (error) {
    console.error('Error saving draft:', error)
    return { data: null, error: 'Failed to save draft' }
  }
}

// Get all drafts
export function getDrafts(): Record<string, any> {
  try {
    const drafts = localStorage.getItem('event_drafts')
    return drafts ? JSON.parse(drafts) : {}
  } catch (error) {
    console.error('Error getting drafts:', error)
    return {}
  }
}

// Delete a draft
export function deleteDraft(draftId: string): boolean {
  try {
    const drafts = getDrafts()
    delete drafts[draftId]
    localStorage.setItem('event_drafts', JSON.stringify(drafts))
    return true
  } catch (error) {
    console.error('Error deleting draft:', error)
    return false
  }
}

// Publish a draft as a live event
export async function publishDraft(draftId: string): Promise<{ data: any | null; error: string | null }> {
  try {
    const drafts = getDrafts()
    const draft = drafts[draftId]

    if (!draft) {
      return { data: null, error: 'Draft not found' }
    }

    // Create the event from the draft
    const { data, error } = await createEvent({
      title: draft.title,
      description: draft.description,
      date: draft.date,
      time: draft.time,
      location: draft.location,
      category: draft.category,
      organizer: draft.organizer,
      organizer_id: draft.organizer_id,
      image_url: draft.image_url,
    })

    if (error) {
      return { data: null, error }
    }

    // Delete the draft after successful publication
    deleteDraft(draftId)

    return { data, error: null }
  } catch (error) {
    console.error('Error publishing draft:', error)
    return { data: null, error: 'Failed to publish draft' }
  }
}

// Upload image to Supabase Storage
export async function uploadEventImage(file: File, eventId: string): Promise<{ url: string | null; error: string | null }> {
  try {
    const fileExt = file.name.split('.').pop()
    const fileName = `${eventId}.${fileExt}`
    const filePath = `event-images/${fileName}`

    const { error: uploadError } = await supabase.storage
      .from('events')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true
      })

    if (uploadError) {
      console.error('Error uploading image:', uploadError)
      return { url: null, error: uploadError.message }
    }

    const { data } = supabase.storage
      .from('events')
      .getPublicUrl(filePath)

    return { url: data.publicUrl, error: null }
  } catch (error) {
    console.error('Unexpected error uploading image:', error)
    return { url: null, error: 'Failed to upload image' }
  }
}

// Get all events
export async function getEvents(): Promise<{ data: Event[] | null; error: string | null }> {
  try {
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .order('date', { ascending: true })

    if (error) {
      console.error('Error fetching events:', error)
      return { data: null, error: error.message }
    }

    return { data, error: null }
  } catch (error) {
    console.error('Unexpected error fetching events:', error)
    return { data: null, error: 'Failed to fetch events' }
  }
}

// Get event by ID
export async function getEventById(id: string): Promise<{ data: Event | null; error: string | null }> {
  try {
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching event:', error)
      return { data: null, error: error.message }
    }

    return { data, error: null }
  } catch (error) {
    console.error('Unexpected error fetching event:', error)
    return { data: null, error: 'Failed to fetch event' }
  }
}

// Update event
export async function updateEvent(id: string, updates: EventUpdate): Promise<{ data: Event | null; error: string | null }> {
  try {
    const { data, error } = await supabase
      .from('events')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating event:', error)
      return { data: null, error: error.message }
    }

    return { data, error: null }
  } catch (error) {
    console.error('Unexpected error updating event:', error)
    return { data: null, error: 'Failed to update event' }
  }
}

// Delete event
export async function deleteEvent(id: string): Promise<{ error: string | null }> {
  try {
    const { error } = await supabase
      .from('events')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting event:', error)
      return { error: error.message }
    }

    return { error: null }
  } catch (error) {
    console.error('Unexpected error deleting event:', error)
    return { error: 'Failed to delete event' }
  }
}

// Validate event form data
export function validateEventForm(formData: EventFormData): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!formData.title.trim()) {
    errors.push('Event title is required')
  }

  if (!formData.description.trim()) {
    errors.push('Event description is required')
  }

  if (!formData.date) {
    errors.push('Event date is required')
  } else {
    const eventDate = new Date(formData.date)
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    if (eventDate < today) {
      errors.push('Event date cannot be in the past')
    }
  }

  if (!formData.time) {
    errors.push('Event time is required')
  }

  if (!formData.venue.trim()) {
    errors.push('Event venue is required')
  }

  if (!formData.category) {
    errors.push('Event category is required')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}
