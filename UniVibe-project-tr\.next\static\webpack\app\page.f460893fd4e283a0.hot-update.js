"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/events.ts":
/*!***********************!*\
  !*** ./lib/events.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEvent: () => (/* binding */ createEvent),\n/* harmony export */   deleteDraft: () => (/* binding */ deleteDraft),\n/* harmony export */   deleteEvent: () => (/* binding */ deleteEvent),\n/* harmony export */   getDrafts: () => (/* binding */ getDrafts),\n/* harmony export */   getEventById: () => (/* binding */ getEventById),\n/* harmony export */   getEvents: () => (/* binding */ getEvents),\n/* harmony export */   publishDraft: () => (/* binding */ publishDraft),\n/* harmony export */   saveDraft: () => (/* binding */ saveDraft),\n/* harmony export */   updateEvent: () => (/* binding */ updateEvent),\n/* harmony export */   uploadEventImage: () => (/* binding */ uploadEventImage),\n/* harmony export */   validateEventForm: () => (/* binding */ validateEventForm)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n\n// Create a new event\nasync function createEvent(eventData) {\n    try {\n        console.log('Creating event with data:', eventData);\n        const insertData = {\n            title: eventData.title,\n            description: eventData.description,\n            date: eventData.date,\n            time: eventData.time,\n            location: eventData.location,\n            category: eventData.category,\n            organizer: eventData.organizer,\n            organizer_id: eventData.organizer_id,\n            image_url: eventData.image_url,\n            attendees_count: 0\n        };\n        console.log('Insert data:', insertData);\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').insert(insertData).select().single();\n        if (error) {\n            console.error('Error creating event:', error);\n            return {\n                data: null,\n                error: error.message || 'Unknown database error'\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error creating event:', error);\n        return {\n            data: null,\n            error: 'An unexpected error occurred'\n        };\n    }\n}\n// Save event as draft (using a separate drafts table or a flag)\nasync function saveDraft(eventData) {\n    try {\n        // For now, we'll save drafts in localStorage\n        // In a real app, you might want a separate drafts table or a is_draft flag\n        const drafts = getDrafts();\n        const draftId = \"draft_\".concat(Date.now());\n        const newDraft = {\n            id: draftId,\n            ...eventData,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        drafts[draftId] = newDraft;\n        localStorage.setItem('event_drafts', JSON.stringify(drafts));\n        return {\n            data: newDraft,\n            error: null\n        };\n    } catch (error) {\n        console.error('Error saving draft:', error);\n        return {\n            data: null,\n            error: 'Failed to save draft'\n        };\n    }\n}\n// Get all drafts\nfunction getDrafts() {\n    try {\n        const drafts = localStorage.getItem('event_drafts');\n        return drafts ? JSON.parse(drafts) : {};\n    } catch (error) {\n        console.error('Error getting drafts:', error);\n        return {};\n    }\n}\n// Delete a draft\nfunction deleteDraft(draftId) {\n    try {\n        const drafts = getDrafts();\n        delete drafts[draftId];\n        localStorage.setItem('event_drafts', JSON.stringify(drafts));\n        return true;\n    } catch (error) {\n        console.error('Error deleting draft:', error);\n        return false;\n    }\n}\n// Publish a draft as a live event\nasync function publishDraft(draftId) {\n    try {\n        const drafts = getDrafts();\n        const draft = drafts[draftId];\n        if (!draft) {\n            return {\n                data: null,\n                error: 'Draft not found'\n            };\n        }\n        // Validate required fields\n        if (!draft.title || !draft.date || !draft.time || !draft.location || !draft.category || !draft.organizer) {\n            return {\n                data: null,\n                error: 'Draft is missing required fields. Please edit the draft and ensure all fields are filled.'\n            };\n        }\n        // Create the event from the draft\n        const { data, error } = await createEvent({\n            title: draft.title,\n            description: draft.description || '',\n            date: draft.date,\n            time: draft.time,\n            location: draft.location,\n            category: draft.category,\n            organizer: draft.organizer,\n            organizer_id: draft.organizer_id || null,\n            image_url: draft.image_url || null\n        });\n        if (error) {\n            return {\n                data: null,\n                error\n            };\n        }\n        // Delete the draft after successful publication\n        deleteDraft(draftId);\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Error publishing draft:', error);\n        return {\n            data: null,\n            error: 'Failed to publish draft'\n        };\n    }\n}\n// Upload image to Supabase Storage\nasync function uploadEventImage(file, eventId) {\n    try {\n        console.log('Uploading image:', {\n            fileName: file.name,\n            fileSize: file.size,\n            eventId\n        });\n        // Validate file\n        if (!file) {\n            return {\n                url: null,\n                error: 'No file provided'\n            };\n        }\n        // Check file size (50MB limit)\n        if (file.size > 50 * 1024 * 1024) {\n            return {\n                url: null,\n                error: 'File size too large. Maximum size is 50MB.'\n            };\n        }\n        // Check file type\n        const allowedTypes = [\n            'image/jpeg',\n            'image/png',\n            'image/webp',\n            'image/gif'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            return {\n                url: null,\n                error: 'Invalid file type. Please upload a JPEG, PNG, WebP, or GIF image.'\n            };\n        }\n        const fileExt = file.name.split('.').pop();\n        const fileName = \"\".concat(eventId, \"_\").concat(Date.now(), \".\").concat(fileExt);\n        const filePath = \"event-images/\".concat(fileName);\n        console.log('Uploading to path:', filePath);\n        const { data: uploadData, error: uploadError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from('events').upload(filePath, file, {\n            cacheControl: '3600',\n            upsert: true\n        });\n        if (uploadError) {\n            console.error('Error uploading image:', uploadError);\n            return {\n                url: null,\n                error: uploadError.message || 'Failed to upload image to storage'\n            };\n        }\n        console.log('Upload successful:', uploadData);\n        const { data } = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from('events').getPublicUrl(filePath);\n        console.log('Public URL generated:', data.publicUrl);\n        return {\n            url: data.publicUrl,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error uploading image:', error);\n        return {\n            url: null,\n            error: 'Failed to upload image'\n        };\n    }\n}\n// Get all events\nasync function getEvents() {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').select('*').order('date', {\n            ascending: true\n        });\n        if (error) {\n            console.error('Error fetching events:', error);\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error fetching events:', error);\n        return {\n            data: null,\n            error: 'Failed to fetch events'\n        };\n    }\n}\n// Get event by ID\nasync function getEventById(id) {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').select('*').eq('id', id).single();\n        if (error) {\n            console.error('Error fetching event:', error);\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error fetching event:', error);\n        return {\n            data: null,\n            error: 'Failed to fetch event'\n        };\n    }\n}\n// Update event\nasync function updateEvent(id, updates) {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').update(updates).eq('id', id).select().single();\n        if (error) {\n            console.error('Error updating event:', error);\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error updating event:', error);\n        return {\n            data: null,\n            error: 'Failed to update event'\n        };\n    }\n}\n// Delete event\nasync function deleteEvent(id) {\n    try {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').delete().eq('id', id);\n        if (error) {\n            console.error('Error deleting event:', error);\n            return {\n                error: error.message\n            };\n        }\n        return {\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error deleting event:', error);\n        return {\n            error: 'Failed to delete event'\n        };\n    }\n}\n// Validate event form data\nfunction validateEventForm(formData) {\n    const errors = [];\n    if (!formData.title.trim()) {\n        errors.push('Event title is required');\n    }\n    if (!formData.description.trim()) {\n        errors.push('Event description is required');\n    }\n    if (!formData.date) {\n        errors.push('Event date is required');\n    } else {\n        const eventDate = new Date(formData.date);\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        if (eventDate < today) {\n            errors.push('Event date cannot be in the past');\n        }\n    }\n    if (!formData.time) {\n        errors.push('Event time is required');\n    }\n    if (!formData.venue.trim()) {\n        errors.push('Event venue is required');\n    }\n    if (!formData.category) {\n        errors.push('Event category is required');\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/events.ts\n"));

/***/ })

});