"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_filter_pills__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/filter-pills */ \"(app-pages-browser)/./components/filter-pills.tsx\");\n/* harmony import */ var _components_event_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/event-card */ \"(app-pages-browser)/./components/event-card.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _lib_events__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/events */ \"(app-pages-browser)/./lib/events.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Mock data\nconst categories = [\n    \"All\",\n    \"Music\",\n    \"Clubs\",\n    \"Academic\",\n    \"Sports\",\n    \"Food\",\n    \"Arts\"\n];\nconst mockEvents = [\n    {\n        id: \"1\",\n        title: \"Spring Music Festival 2024\",\n        date: \"March 15\",\n        time: \"7:00 PM\",\n        location: \"University Quad\",\n        organizer: \"Music Society\",\n        attendees: 234,\n        category: \"Music\",\n        image: \"/placeholder.svg?height=192&width=400\"\n    },\n    {\n        id: \"2\",\n        title: \"Tech Talk: AI in Healthcare\",\n        date: \"March 18\",\n        time: \"2:00 PM\",\n        location: \"Engineering Building, Room 101\",\n        organizer: \"Computer Science Club\",\n        attendees: 89,\n        category: \"Academic\",\n        image: \"/placeholder.svg?height=192&width=400\"\n    },\n    {\n        id: \"3\",\n        title: \"Basketball Championship Finals\",\n        date: \"March 20\",\n        time: \"6:00 PM\",\n        location: \"Sports Complex\",\n        organizer: \"Athletics Department\",\n        attendees: 456,\n        category: \"Sports\",\n        image: \"/placeholder.svg?height=192&width=400\"\n    },\n    {\n        id: \"4\",\n        title: \"Art Gallery Opening Night\",\n        date: \"March 22\",\n        time: \"5:30 PM\",\n        location: \"Student Center Gallery\",\n        organizer: \"Art Club\",\n        attendees: 67,\n        category: \"Arts\",\n        image: \"/placeholder.svg?height=192&width=400\"\n    },\n    {\n        id: \"5\",\n        title: \"Food Truck Festival\",\n        date: \"March 25\",\n        time: \"11:00 AM\",\n        location: \"Campus Green\",\n        organizer: \"Student Government\",\n        attendees: 312,\n        category: \"Food\",\n        image: \"/placeholder.svg?height=192&width=400\"\n    }\n];\nfunction HomePage() {\n    _s();\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"All\");\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    // Fetch events from Supabase\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const fetchEvents = {\n                \"HomePage.useEffect.fetchEvents\": async ()=>{\n                    try {\n                        const { data, error } = await (0,_lib_events__WEBPACK_IMPORTED_MODULE_4__.getEvents)();\n                        if (error) {\n                            console.error('Error fetching events:', error);\n                            // Fallback to mock data if there's an error\n                            setEvents(mockEvents);\n                        } else {\n                            // Transform Supabase data to match EventCard props\n                            const transformedEvents = (data === null || data === void 0 ? void 0 : data.map({\n                                \"HomePage.useEffect.fetchEvents\": (event)=>({\n                                        id: event.id,\n                                        title: event.title,\n                                        date: formatDate(event.date),\n                                        time: event.time,\n                                        location: event.location,\n                                        organizer: event.organizer,\n                                        attendees: event.attendees_count || 0,\n                                        category: event.category,\n                                        image: event.image_url || \"/placeholder.svg?height=192&width=400\"\n                                    })\n                            }[\"HomePage.useEffect.fetchEvents\"])) || [];\n                            setEvents(transformedEvents);\n                        }\n                    } catch (error) {\n                        console.error('Unexpected error fetching events:', error);\n                        setEvents(mockEvents);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"HomePage.useEffect.fetchEvents\"];\n            fetchEvents();\n        }\n    }[\"HomePage.useEffect\"], []);\n    // Helper function to format date\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"TBD\";\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    const filteredEvents = activeCategory === \"All\" ? events : events.filter((event)=>event.category === activeCategory);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-2 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold gradient-text\",\n                        children: \"Discover Amazing Events\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Find and join events happening around your campus\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-5 h-5 text-accent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            \"Categories\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_pills__WEBPACK_IMPORTED_MODULE_1__.FilterPills, {\n                        categories: categories,\n                        activeCategory: activeCategory,\n                        onCategoryChange: setActiveCategory\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold\",\n                                children: activeCategory === \"All\" ? \"All Events\" : \"\".concat(activeCategory, \" Events\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: [\n                                    filteredEvents.length,\n                                    \" event\",\n                                    filteredEvents.length !== 1 ? \"s\" : \"\",\n                                    \" found\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-6\",\n                        children: loading ? // Loading skeleton\n                        Array.from({\n                            length: 3\n                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-pulse\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-muted rounded-lg h-64 w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 17\n                                }, this)\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 15\n                            }, this)) : filteredEvents.length > 0 ? filteredEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_event_card__WEBPACK_IMPORTED_MODULE_2__.EventCard, {\n                                ...event\n                            }, event.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 15\n                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"No events found for this category.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            filteredEvents.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-16 space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-8 h-8 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-semibold text-muted-foreground\",\n                                children: \"No events found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Try selecting a different category\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n                lineNumber: 172,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\page.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"t68ASv+heyQHjqcUeMCGmVe7CMQ=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});